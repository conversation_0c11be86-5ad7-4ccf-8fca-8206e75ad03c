make clean
make

echo "Now Checking US Constitution"
./main -f1 test.txt -f2 a11f3.txt < as5Checks.txt > resultsOfUSConstitution.txt
if diff ogOfUSConstitution.txt resultsOfUSConstitution.txt > /dev/null; then
    echo "Passed USConstitution Checks"
else
    echo "Failed USConstitution Checks"
fi

echo "Now Checking results LineNums"
./main -f1 test.txt -f2 a11f4.txt < as5Checks.txt > resultsLineNums.txt

if diff ogLineNums.txt resultsLineNums.txt > /dev/null; then
    echo "Passed LineNum Checks"
else
    echo "Failed LineNum Checks"
fi

echo "Now Checking movie reviews"
./main -f1 test.txt -f2 a11f6.txt < as5Checks.txt > resultsReviews.txt
if diff ogReviews.txt resultsReviews.txt > /dev/null; then
    echo "Passed Reviews Checks"
else
    echo "Failed Reviews Checks (If you failed but have more lines, then you are okay - as long as the previous ones are the same)"
fi

echo "Now Checking Plots"
./main -f1 test.txt -f2 a11f6.txt < as5Checks.txt > resultsPlots.txt
if diff ogReviews.txt resultsReviews.txt > /dev/null; then
    echo "Passed Reviews Checks"
else
    echo "Failed Reviews Checks (If you failed but have more lines, then you are okay - as long as the previous ones are the same)"
fi
