     1                                 %line 1+1 ast5procs.asm
     2                                 
     3                                 
     4                                 
     5                                 
     6                                 
     7                                 
     8                                 
     9                                 
    10                                 
    11                                 
    12                                 
    13                                 [section .data]
    14                                 TRUE equ 1
    15                                 FALSE equ 0
    16                                 NULL equ 0
    17                                 LF equ 10
    18                                 NEWLINE equ 10
    19                                 
    20                                 SYS_read equ 0
    21                                 SYS_write equ 1
    22                                 SYS_open equ 2
    23                                 SYS_close equ 3
    24                                 SYS_fork equ 57
    25                                 SYS_exit equ 60
    26                                 SYS_creat equ 85
    27                                 SYS_time equ 201
    28                                 
    29                                 O_RDONLY equ 000000
    30                                 O_WRONLY equ 000001
    31                                 O_RDWR equ 000002
    32                                 
    33                                 STDIN equ 0
    34                                 STDOUT equ 1
    35                                 STDERR equ 2
    36                                 
    37                                 O_CREAT equ 0x40
    38                                 O_TRUNC equ 0x200
    39                                 O_APPEND equ 0x400
    40                                 
    41 00000000 496E7075742061206E-    InputMessage db "Input a number: ",NEWLINE, NULL
    42 00000000 756D6265723A200A00 
    43 00000012 596F75206761766520-    badInput db "You gave an invalid number, try again. ",NEWLINE, "New ", NULL
    44 00000012 616E20696E76616C69-
    45 00000012 64206E756D6265722C-
    46 00000012 207472792061676169-
    47 00000012 6E2E200A4E65772000 
    48 0000003F 0A00                   nlMessage db NEWLINE, NULL
    49 00000041 2000                   spaceMessage db " ",NULL
    50 00000043 496E76616C69642057-    outputMessage db "Invalid Word found: ",NULL
    51 00000043 6F726420666F756E64-
    52 00000043 3A2000             
    53                                 
    54                                 BUFFSIZE equ 500000
    55 00000058 002F685900000000       maxLIMIT dq 1500000000
    56 00000060 00D197A6FFFFFFFF       minLIMIT dq -1500000000
    57                                 
    58                                 [section .bss]
    59                                 
    60 00000000 <gap>                  BUFFER resb BUFFSIZE
    61                                 
    62                                 [section .text]
    63                                 
    64                                 [global checkParams]
    65                                 checkParams:
    66                                 
    67                                 
    68                                 
    69                                 
    70                                 
    71                                 
    72 00000000 55                      push rbp
    73 00000001 4889E5                  mov rbp, rsp
    74 00000004 53                      push rbx
    75 00000005 4154                    push r12
    76 00000007 4155                    push r13
    77 00000009 4156                    push r14
    78 0000000B 4157                    push r15
    79                                 
    80 0000000D 4989F0                  mov r8, rsi
    81 00000010 4989D1                  mov r9, rdx
    82 00000013 4989CA                  mov r10, rcx
    83                                 
    84                                 
    85 00000016 4883FF05                cmp rdi, 5
    86 0000001A 0F857D000000            jne fail
    87                                 
    88                                 
    89 00000020 498B5808                mov rbx, qword [r8 + 8]
    90                                 
    91                                 
    92                                 
    93 00000024 803B2D                  cmp byte [rbx], '-'
    94 00000027 7578                    jne fail
    95 00000029 807B0166                cmp byte [rbx + 1], 'f'
    96 0000002D 7572                    jne fail
    97 0000002F 807B0231                cmp byte [rbx + 2], '1'
    98 00000033 756C                    jne fail
    99 00000035 807B0300                cmp byte [rbx + 3], 0
   100 00000039 7566                    jne fail
   101                                 
   102 0000003B 498B5818                mov rbx, qword [r8 + 24]
   103 0000003F 803B2D                  cmp byte [rbx], '-'
   104 00000042 755D                    jne fail
   105 00000044 807B0166                cmp byte [rbx + 1], 'f'
   106 00000048 7557                    jne fail
   107 0000004A 807B0232                cmp byte [rbx + 2], '2'
   108 0000004E 7551                    jne fail
   109 00000050 807B0300                cmp byte [rbx + 3], 0
   110 00000054 754B                    jne fail
   111                                 
   112 00000056 498B7810                mov rdi, qword [r8 + 16]
   113 0000005A 48C7C641020000          mov rsi, O_CREAT | O_WRONLY | O_TRUNC
   114 00000061 48C7C2A4010000          mov rdx, 0644
   115 00000068 48C7C055000000          mov rax, SYS_creat
   116 0000006F 0F05                    syscall
   117 00000071 4883F800                cmp rax, 0
   118 00000075 7C2A                    jl fail
   119 00000077 498901                  mov qword [r9], rax
   120                                 
   121 0000007A 498B7820                mov rdi, qword [r8 + 32]
   122 0000007E 48C7C600000000          mov rsi, O_RDONLY
   123 00000085 4831D2                  xor rdx, rdx
   124 00000088 48C7C002000000          mov rax, SYS_open
   125 0000008F 0F05                    syscall
   126 00000091 4883F800                cmp rax, 0
   127 00000095 7C0A                    jl fail
   128 00000097 498902                  mov qword [r10], rax
   129                                 
   130 0000009A 48C7C001000000          mov rax, TRUE
   131 000000A1 EB05                    jmp doneWithCheckLoop
   132                                 
   133                                 fail:
   134 000000A3 48C7C000000000          mov rax, FALSE
   135                                 
   136                                 doneWithCheckLoop:
   137 000000AA 415F                    pop r15
   138 000000AC 415E                    pop r14
   139 000000AE 415D                    pop r13
   140 000000B0 415C                    pop r12
   141 000000B2 5B                      pop rbx
   142 000000B3 5D                      pop rbp
   143 000000B4 C3                     ret
   144                                 
   145                                 [global getWord]
   146                                 getWord:
   147 000000B5 55                      push rbp
   148 000000B6 4889E5                  mov rbp, rsp
   149 000000B9 53                      push rbx
   150 000000BA 4154                    push r12
   151 000000BC 4155                    push r13
   152 000000BE 4156                    push r14
   153 000000C0 4157                    push r15
   154                                 
   155 000000C2 4989FC                  mov r12, rdi
   156 000000C5 4989F5                  mov r13, rsi
   157 000000C8 4989D6                  mov r14, rdx
   158 000000CB 4989CF                  mov r15, rcx
   159 000000CE 48C7C300000000          mov rbx, 0
   160                                 
   161                                 skipSpacesLoop:
   162 000000D5 498B3F                  mov rdi, qword [r15]
   163 000000D8 48C7C6[00000000]        mov rsi, BUFFER
   164 000000DF 48C7C201000000          mov rdx, 1
   165 000000E6 48C7C000000000          mov rax, SYS_read
   166 000000ED 0F05                    syscall
   167 000000EF 4883F800                cmp rax, 0
   168 000000F3 0F8EC8000000            jle getEOF
   169                                 
   170 000000F9 8A0425[00000000]        mov al, byte [BUFFER]
   171 00000100 3C20                    cmp al, ' '
   172 00000102 74CF                    je skipSpacesLoop
   173 00000104 3C0A                    cmp al, 10
   174 00000106 74CB                    je skipSpacesLoop
   175 00000108 3C09                    cmp al, 9
   176 0000010A 74C7                    je skipSpacesLoop
   177                                 
   178 0000010C 4188041C                mov byte [r12 + rbx], al
   179 00000110 48FFC3                  inc rbx
   180 00000113 4C39F3                  cmp rbx, r14
   181 00000116 7D52                    jge wordTooLong
   182                                 
   183                                 readGetWordLoop:
   184 00000118 498B3F                  mov rdi, qword [r15]
   185 0000011B 48C7C6[00000000]        mov rsi, BUFFER
   186 00000122 48C7C201000000          mov rdx, 1
   187 00000129 48C7C000000000          mov rax, SYS_read
   188 00000130 0F05                    syscall
   189 00000132 4883F800                cmp rax, 0
   190 00000136 7E1F                    jle endwordofGetWordLoop
   191                                 
   192 00000138 8A0425[00000000]        mov al, byte [BUFFER]
   193 0000013F 3C20                    cmp al, ' '
   194 00000141 7414                    je endwordofGetWordLoop
   195 00000143 3C0A                    cmp al, 10
   196 00000145 7410                    je endwordofGetWordLoop
   197 00000147 3C09                    cmp al, 9
   198 00000149 740C                    je endwordofGetWordLoop
   199                                 
   200 0000014B 4C39F3                  cmp rbx, r14
   201 0000014E 7D1A                    jge wordTooLong
   202                                 
   203 00000150 4188041C                mov byte [r12 + rbx], al
   204 00000154 48FFC3                  inc rbx
   205 00000157 EBBD                    jmp readGetWordLoop
   206                                 
   207                                 endwordofGetWordLoop:
   208 00000159 41C6041C00              mov byte [r12 + rbx], 0
   209 0000015E 41C6450001              mov byte [r13], TRUE
   210 00000163 48C7C001000000          mov rax, TRUE
   211 0000016A EB60                    jmp done
   212                                 
   213                                 wordTooLong:
   214 0000016C 4188041C                mov byte [r12 + rbx], al
   215 00000170 48FFC3                  inc rbx
   216                                 
   217                                 wordTooLongLoop:
   218 00000173 498B3F                  mov rdi, qword [r15]
   219 00000176 48C7C6[00000000]        mov rsi, BUFFER
   220 0000017D 48C7C201000000          mov rdx, 1
   221 00000184 48C7C000000000          mov rax, SYS_read
   222 0000018B 0F05                    syscall
   223 0000018D 4883F800                cmp rax, 0
   224 00000191 7E1F                    jle endNotValid
   225                                 
   226 00000193 8A0425[00000000]        mov al, byte [BUFFER]
   227 0000019A 3C20                    cmp al, ' '
   228 0000019C 7414                    je endNotValid
   229 0000019E 3C0A                    cmp al, 10
   230 000001A0 7410                    je endNotValid
   231 000001A2 3C09                    cmp al, 9
   232 000001A4 740C                    je endNotValid
   233                                 
   234 000001A6 4C39F3                  cmp rbx, r14
   235 000001A9 7D05                    jge skipTilDone
   236 000001AB 4188041C                mov byte [r12 + rbx], al
   237 000001AF 48FFC3                  inc rbx
   238                                 
   239                                 skipTilDone:
   240 000001B2 EBBD                    jmp wordTooLongLoop
   241                                 
   242                                 endNotValid:
   243 000001B4 41C6041C00              mov byte [r12 + rbx], 0
   244 000001B9 41C6450000              mov byte [r13], FALSE
   245 000001BE 48C7C001000000          mov rax, TRUE
   246 000001C5 EB05                    jmp done
   247                                 
   248                                 getEOF:
   249 000001C7 48C7C000000000          mov rax, FALSE
   250                                 
   251                                 done:
   252 000001CE 415F                    pop r15
   253 000001D0 415E                    pop r14
   254 000001D2 415D                    pop r13
   255 000001D4 415C                    pop r12
   256 000001D6 5B                      pop rbx
   257 000001D7 5D                      pop rbp
   258 000001D8 C3                     ret
   259                                 
   260                                 [global printWord]
   261                                 printWord:
   262 000001D9 48C7C001000000          mov rax, SYS_write
   263 000001E0 0F05                    syscall
   264 000001E2 4080FE00                cmp sil, FALSE
   265 000001E6 7523                    jne printWord_done
   266                                 
   267 000001E8 57                      push rdi
   268 000001E9 56                      push rsi
   269 000001EA 52                      push rdx
   270 000001EB 50                      push rax
   271                                 
   272 000001EC 48C7C7[00000000]        mov rdi, outputMessage
   273 000001F3 E810010000              call printString
   274                                 
   275 000001F8 58                      pop rax
   276 000001F9 5A                      pop rdx
   277 000001FA 5E                      pop rsi
   278 000001FB 5F                      pop rdi
   279 000001FC E807010000              call printString
   280                                 
   281 00000201 48C7C7[00000000]        mov rdi, nlMessage
   282 00000208 E8FB000000              call printString
   283                                 
   284                                 printWord_done:
   285 0000020D C3                     ret
   286                                 
   287                                 [global getNumber]
   288                                 getNumber:
   289 0000020E 55                      push rbp
   290 0000020F 4889E5                  mov rbp, rsp
   291 00000212 53                      push rbx
   292 00000213 4154                    push r12
   293 00000215 4155                    push r13
   294 00000217 4156                    push r14
   295                                 
   296 00000219 4989FC                  mov r12, rdi
   297 0000021C 4989F5                  mov r13, rsi
   298 0000021F 4989D6                  mov r14, rdx
   299                                 
   300 00000222 41FF06                  inc dword [r14]
   301                                 
   302 00000225 48C7C7[00000000]        mov rdi, InputMessage
   303 0000022C E8D7000000              call printString
   304                                 
   305 00000231 418B1E                  mov ebx, dword [r14]
   306                                 
   307 00000234 83FB01                  cmp ebx, 1
   308 00000237 7431                    je invalidInput
   309 00000239 83FB02                  cmp ebx, 2
   310 0000023C 742C                    je invalidInput
   311 0000023E 83FB03                  cmp ebx, 3
   312 00000241 7427                    je invalidInput
   313 00000243 83FB07                  cmp ebx, 7
   314 00000246 7422                    je invalidInput
   315 00000248 83FB0E                  cmp ebx, 14
   316 0000024B 741D                    je invalidInput
   317                                 
   318 0000024D 83FB0F                  cmp ebx, 15
   319 00000250 7D2D                    jge endInput
   320                                 
   321 00000252 41C6042431              mov byte [r12], '1'
   322 00000257 41C644240132            mov byte [r12 + 1], '2'
   323 0000025D 41C644240200            mov byte [r12 + 2], 0
   324 00000263 48C7C001000000          mov rax, TRUE
   325 0000026A EB1A                    jmp doneWithGetNumber
   326                                 
   327                                 invalidInput:
   328 0000026C 48C7C7[00000000]        mov rdi, badInput
   329 00000273 E890000000              call printString
   330 00000278 48C7C001000000          mov rax, TRUE
   331 0000027F EB05                    jmp doneWithGetNumber
   332                                 
   333                                 endInput:
   334 00000281 48C7C000000000          mov rax, FALSE
   335                                 
   336                                 doneWithGetNumber:
   337 00000288 415E                    pop r14
   338 0000028A 415D                    pop r13
   339 0000028C 415C                    pop r12
   340 0000028E 5B                      pop rbx
   341 0000028F 5D                      pop rbp
   342 00000290 C3                     ret
   343                                 
   344                                 [global saveNumbers]
   345                                 saveNumbers:
   346 00000291 55                      push rbp
   347 00000292 4889E5                  mov rbp, rsp
   348 00000295 53                      push rbx
   349 00000296 4154                    push r12
   350 00000298 4155                    push r13
   351                                 
   352 0000029A 4989FC                  mov r12, rdi
   353 0000029D 4989F5                  mov r13, rsi
   354 000002A0 4889D3                  mov rbx, rdx
   355                                 
   356 000002A3 488B3B                  mov rdi, qword [rbx]
   357 000002A6 4C89E6                  mov rsi, r12
   358 000002A9 4C89EA                  mov rdx, r13
   359 000002AC 48C7C001000000          mov rax, SYS_write
   360 000002B3 0F05                    syscall
   361                                 
   362 000002B5 488B3B                  mov rdi, qword [rbx]
   363 000002B8 48C7C6[00000000]        mov rsi, nlMessage
   364 000002BF 48C7C201000000          mov rdx, 1
   365 000002C6 48C7C001000000          mov rax, SYS_write
   366 000002CD 0F05                    syscall
   367                                 
   368 000002CF 415D                    pop r13
   369 000002D1 415C                    pop r12
   370 000002D3 5B                      pop rbx
   371 000002D4 5D                      pop rbp
   372 000002D5 48C7C001000000          mov rax, TRUE
   373 000002DC C3                     ret
   374                                 
   375                                 [global closeFile]
   376                                 closeFile:
   377 000002DD 55                      push rbp
   378 000002DE 4889E5                  mov rbp, rsp
   379 000002E1 53                      push rbx
   380                                 
   381 000002E2 4889FB                  mov rbx, rdi
   382 000002E5 488B3B                  mov rdi, qword [rbx]
   383 000002E8 48C7C003000000          mov rax, SYS_close
   384 000002EF 0F05                    syscall
   385 000002F1 5B                      pop rbx
   386 000002F2 5D                      pop rbp
   387 000002F3 C3                     ret
   388                                 
   389                                 [global getLength]
   390                                 getLength:
   391 000002F4 48C7C000000000          mov rax, 0
   392                                  strCountLoop2:
   393 000002FB 803C0700                cmp byte [rdi + rax], NULL
   394 000002FF 7403                    je strCountLoopDone2
   395 00000301 48FFC0                  inc rax
   396 00000304 EBF3                    jmp strCountLoop2
   397                                  strCountLoopDone2:
   398 00000306 4883F800                cmp rax, 0
   399 0000030A 74FE                    je printStringDone2
   400                                  printStringDone2:
   401 0000030C C3                     ret
   402                                 
   403                                 [global printString]
   404                                 printString:
   405                                 
   406                                 
   407                                 
   408 0000030D 48C7C200000000          mov rdx, 0
   409                                  strCountLoop:
   410 00000314 803C1700                cmp byte [rdi+rdx], NULL
   411 00000318 7403                    je strCountLoopDone
   412 0000031A 48FFC2                  inc rdx
   413 0000031D EBF3                    jmp strCountLoop
   414                                  strCountLoopDone:
   415 0000031F 4883FA00                cmp rdx, 0
   416 00000323 7411                    je printStringDone
   417                                 
   418                                 
   419                                 
   420                                 
   421 00000325 48C7C001000000          mov rax, SYS_write
   422 0000032C 4889FE                  mov rsi, rdi
   423 0000032F 48C7C701000000          mov rdi, STDOUT
   424                                 
   425 00000336 0F05                    syscall
   426                                 
   427                                 
   428                                 
   429                                 
   430                                  printStringDone:
   431 00000338 C3                     ret
