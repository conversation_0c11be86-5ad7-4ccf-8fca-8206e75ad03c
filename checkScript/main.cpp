// Provided Main
#include <cstdlib>
#include <iostream>
#include <cstdio>
#include <fstream>
#include <string>

using namespace std;

extern "C" bool checkParams(int, char *[],  long*,  long*);
extern "C" bool getWord(char [], bool*, int,  long*);
extern "C" void printWord(char [], bool);
extern "C" bool getNumber(char [], int, int*);
extern "C" bool saveNumbers(char [], int,  long*);
extern "C" void closeFile( long*);


int main(int argc, char* argv[]){

    cout << endl;  // Add blank line at beginning
    std::remove(argv[2]);

    static	const	unsigned int	MAXNUMLENGTH=20;
    long fileDescriptorNumbers =0;
    long fileDescriptorFile =0;
    char currentNum[MAXNUMLENGTH+1]; 

    if(!checkParams(argc, argv, &fileDescriptorNumbers, &fileDescriptorFile)){
        cout<<"There is an issue with your command line arguments - try again."<<endl;
        exit(0);
    }
    int index = 0;
    while(getNumber(currentNum, MAXNUMLENGTH,  &index)){
        saveNumbers(currentNum, index, &fileDescriptorNumbers);
    }

    closeFile(&fileDescriptorNumbers);
    cout<<"Your file: "<<argv[2]<<" Should now have the DuoDecimal numbers you've inputted."<<endl;

    std::ifstream testFile(argv[2]);
        if (testFile.is_open()) {
        std::string line;

        while (std::getline(testFile, line)) {
            std::cout << line << std::endl;
        }

        testFile.close();
    }

    static	const	unsigned int	MAXWORDLENGTH=12;
    char currentWord[MAXWORDLENGTH+1];
    bool validWord = false;
	while (getWord(currentWord, &validWord, MAXWORDLENGTH, &fileDescriptorFile)) {
		printWord(currentWord, validWord);
	}

    closeFile(&fileDescriptorFile);

    return 0;
}