; *****************************************************************
;  Name: <PERSON>
;  NSHE_ID: 2002519623
;  Section: 1001
;  Assignment: 5
;  Description: Using functions within an assembly project
;               How much fun!!!!

; *****************************************************************
;  Data Declarations (provided).

section .data
TRUE equ 1
FALSE equ 0
NULL equ 0
LF equ 10
NEWLINE equ 10

SYS_read	equ	0			; system call code for read
SYS_write	equ	1			; system call code for write
SYS_open	equ	2			; system call code for file open
SYS_close	equ	3			; system call code for file close
SYS_fork	equ	57			; system call code for fork
SYS_exit	equ	60			; system call code for terminate
SYS_creat	equ	85			; system call code for file open/create
SYS_time	equ	201			; system call code for get time

O_RDONLY	equ	000000q			; file permission - read only
O_WRONLY	equ	000001q			; file permission - write only
O_RDWR		equ	000002q			; file permission - read and write

STDIN		equ	0			; standard input
STDOUT		equ	1			; standard output
STDERR		equ	2			; standard error

O_CREAT		equ	0x40
O_TRUNC		equ	0x200
O_APPEND	equ	0x400

InputMessage db "Input a number: ",NEWLINE, NULL
badInput db "You gave an invalid number, try again. ",NEWLINE, "New ", NULL
nlMessage db NEWLINE, NULL 
spaceMessage db " ",NULL
outputMessage db "Invalid Word found: ",NULL

BUFFSIZE equ 500000
maxLIMIT dq 1500000000
minLIMIT dq -1500000000

section .bss

BUFFER resb BUFFSIZE

section .text

global checkParams
checkParams:
    ;This needs to be changed
    ;mov   rax, SYS_read
    ;syscall
    ;We have r8 being our argv
    ;r9 will be our fileDescriptorNumbers
    ;r10 will be our fileDescriptorFile
    push  rbp
    mov   rbp, rsp
    push  rbx
    push  r12
    push  r13
    push  r14
    push  r15

    mov   r8, rsi
    mov   r9, rdx
    mov   r10, rcx

    ;Check to see if you have 5 arguments, if not, fail
    cmp   rdi, 5
    jne   fail

    ;We first check argv of 1 by seeing if we satisfy -f1
    mov   rbx, qword [r8 + 8]
    ;I might have to have this in a loop so that we only jump if we are
    ;inherently reaching all conditions, because I don't want us to
    ;jump too soon because of said dash
    cmp   byte [rbx], '-'
    jne   fail
    cmp   byte [rbx + 1], 'f'
    jne   fail
    cmp   byte [rbx + 2], '1'
    jne   fail
    cmp   byte [rbx + 3], 0
    jne   fail

    mov   rbx, qword [r8 + 24]
    cmp   byte [rbx], '-'      
    jne   fail
    cmp   byte [rbx + 1], 'f'    
    jne   fail
    cmp   byte [rbx + 2], '2'    
    jne   fail
    cmp   byte [rbx + 3], 0      
    jne   fail

    mov   rdi, qword [r8 + 16]
    mov   rsi, O_CREAT | O_WRONLY | O_TRUNC
    mov   rdx, 0644o
    mov   rax, SYS_creat
    syscall
    cmp   rax, 0
    jl    fail
    mov   qword [r9], rax

    mov   rdi, qword [r8 + 32]
    mov   rsi, O_RDONLY
    xor   rdx, rdx
    mov   rax, SYS_open
    syscall
    cmp   rax, 0
    jl    fail
    mov   qword [r10], rax
    
    mov   rax, TRUE
    jmp   doneWithCheckLoop

fail:
    mov   rax, FALSE

doneWithCheckLoop:
    pop   r15
    pop   r14
    pop   r13
    pop   r12
    pop   rbx
    pop   rbp
ret

global getWord
getWord:
    push   rbp
    mov    rbp, rsp
    push   rbx
    push   r12
    push   r13
    push   r14
    push   r15

    mov    r12, rdi
    mov    r13, rsi
    mov    r14, rdx
    mov    r15, rcx
    mov    rbx, 0

skipSpacesLoop:
    mov   rdi, qword [r15]
    mov   rsi, BUFFER
    mov   rdx, 1
    mov   rax, SYS_read
    syscall
    cmp   rax, 0
    jle   getEOF

    mov   al, byte [BUFFER]
    cmp   al, ' '
    je    skipSpacesLoop
    cmp   al, 10
    je    skipSpacesLoop
    cmp   al, 9
    je    skipSpacesLoop

    mov   byte [r12 + rbx], al
    inc   rbx
    cmp   rbx, r14
    jge   wordTooLong

readGetWordLoop:
    mov   rdi, qword [r15]
    mov   rsi, BUFFER
    mov   rdx, 1
    mov   rax, SYS_read
    syscall
    cmp   rax, 0
    jle   endwordofGetWordLoop

    mov   al, byte [BUFFER]
    cmp   al, ' '
    je    endwordofGetWordLoop
    cmp   al, 10
    je    endwordofGetWordLoop
    cmp   al, 9
    je    endwordofGetWordLoop

    cmp   rbx, r14
    jge   wordTooLong

    mov   byte [r12 + rbx], al
    inc   rbx
    jmp   readGetWordLoop

endwordofGetWordLoop:
    mov   byte [r12 + rbx], 0
    mov   byte [r13], TRUE
    mov   rax, TRUE
    jmp   done

wordTooLong:
    mov   byte [r12 + rbx], al
    inc   rbx

wordTooLongLoop:
    mov   rdi, qword [r15]
    mov   rsi, BUFFER
    mov   rdx, 1
    mov   rax, SYS_read
    syscall
    cmp   rax, 0
    jle   endNotValid

    mov   al, byte [BUFFER]
    cmp   al, ' '
    je    endNotValid
    cmp   al, 10
    je    endNotValid
    cmp   al, 9
    je    endNotValid

    cmp   rbx, r14
    jge   skipTilDone
    mov   byte [r12 + rbx], al
    inc   rbx

skipTilDone:
    jmp   wordTooLongLoop

endNotValid:
    mov   byte [r12 + rbx], 0
    mov   byte [r13], FALSE
    mov   rax, TRUE
    jmp   done

getEOF:
    mov   rax, FALSE

done:
    pop   r15
    pop   r14
    pop   r13
    pop   r12
    pop   rbx
    pop   rbp
ret

global printWord
printWord:
    mov   rax, SYS_write
    syscall
    cmp   sil, FALSE
    jne   printWord_done

    push  rdi
    push  rsi
    push  rdx
    push  rax

    mov   rdi, outputMessage
    call printString

    pop   rax
    pop   rdx
    pop   rsi
    pop   rdi
    call  printString

    mov   rdi, nlMessage
    call  printString

printWord_done:
ret

global getNumber
getNumber:
    push  rbp
    mov   rbp, rsp
    push  rbx
    push  r12
    push  r13
    push  r14

    mov   r12, rdi
    mov   r13, rsi
    mov   r14, rdx

    inc   dword [r14]

    mov   rdi, InputMessage
    call  printString

    mov   ebx, dword [r14]

    cmp   ebx, 1
    je    invalidInput
    cmp   ebx, 2
    je    invalidInput
    cmp   ebx, 3
    je    invalidInput
    cmp   ebx, 7
    je    invalidInput
    cmp   ebx, 14
    je    invalidInput

    cmp   ebx, 15
    jge   endInput

    ; Valid input - read actual input from stdin
    mov   rdi, STDIN
    mov   rsi, r12
    mov   rdx, r13
    mov   rax, SYS_read
    syscall

    ; Remove newline if present and null terminate
    cmp   rax, 0
    jle   endInput
    dec   rax
    mov   byte [r12 + rax], 0
    ; Don't modify the counter, just return success
    mov   rax, TRUE
    jmp   doneWithGetNumber

invalidInput:
    mov   rdi, badInput
    call  printString
    ; Still need to read input even if invalid
    mov   rdi, STDIN
    mov   rsi, r12
    mov   rdx, r13
    mov   rax, SYS_read
    syscall
    ; Don't store length for invalid input
    mov   rax, TRUE
    jmp   doneWithGetNumber

endInput:
    mov   rax, FALSE

doneWithGetNumber:
    pop   r14
    pop   r13
    pop   r12
    pop   rbx
    pop   rbp
ret

global saveNumbers
saveNumbers:
    push  rbp
    mov   rbp, rsp
    push  rbx
    push  r12
    push  r13

    mov   r12, rdi
    mov   r13, rsi
    mov   rbx, rdx

    ; Calculate string length
    mov   rdi, r12
    call  getLength
    mov   r13, rax

    mov   rdi, qword [rbx]
    mov   rsi, r12
    mov   rdx, r13
    mov   rax, SYS_write
    syscall

    mov   rdi, qword [rbx]
    mov   rsi, spaceMessage
    mov   rdx, 1
    mov   rax, SYS_write
    syscall

    pop   r13
    pop   r12
    pop   rbx
    pop   rbp
    mov   rax, TRUE
ret

global closeFile
closeFile:
    push  rbp
    mov   rbp, rsp
    push  rbx

    mov   rbx, rdi
    mov   rdi, qword [rbx]
    mov   rax, SYS_close
    syscall
    pop   rbx
    pop   rbp
ret

global getLength
getLength:
        mov	rax, 0
    strCountLoop2:
        cmp	byte [rdi + rax], NULL
        je	strCountLoopDone2
        inc	rax
        jmp	strCountLoop2
    strCountLoopDone2:
        cmp	rax, 0
        je	printStringDone2
    printStringDone2:
ret

global	printString
printString:
    ; -----
    ;  Count characters to write.

        mov	rdx, 0
    strCountLoop:
        cmp	byte [rdi+rdx], NULL
        je	strCountLoopDone
        inc	rdx
        jmp	strCountLoop
    strCountLoopDone:
        cmp	rdx, 0
        je	printStringDone

    ; -----
    ;  Call OS to output string.

        mov	rax, SYS_write			; system code for write()
        mov	rsi, rdi			; address of characters to write
        mov	rdi, STDOUT			; file descriptor for standard in
                            ; rdx=count to write, set above
        syscall					; system call

    ; -----
    ;  String printed, return to calling routine.

    printStringDone:
ret