     1                                 %line 1+1 ast5procs.asm
     2                                 
     3                                 
     4                                 
     5                                 
     6                                 
     7                                 
     8                                 
     9                                 
    10                                 
    11                                 
    12                                 
    13                                 [section .data]
    14                                 TRUE equ 1
    15                                 FALSE equ 0
    16                                 NULL equ 0
    17                                 LF equ 10
    18                                 NEWLINE equ 10
    19                                 
    20                                 SYS_read equ 0
    21                                 SYS_write equ 1
    22                                 SYS_open equ 2
    23                                 SYS_close equ 3
    24                                 SYS_fork equ 57
    25                                 SYS_exit equ 60
    26                                 SYS_creat equ 85
    27                                 SYS_time equ 201
    28                                 
    29                                 O_RDONLY equ 000000
    30                                 O_WRONLY equ 000001
    31                                 O_RDWR equ 000002
    32                                 
    33                                 STDIN equ 0
    34                                 STDOUT equ 1
    35                                 STDERR equ 2
    36                                 
    37                                 O_CREAT equ 0x40
    38                                 O_TRUNC equ 0x200
    39                                 O_APPEND equ 0x400
    40                                 
    41 00000000 496E7075742061206E-    InputMessage db "Input a number: ",NEWLINE, NULL
    42 00000000 756D6265723A200A00 
    43 00000012 596F75206761766520-    badInput db "You gave an invalid number, try again. ",NEWLINE, "New ", NULL
    44 00000012 616E20696E76616C69-
    45 00000012 64206E756D6265722C-
    46 00000012 207472792061676169-
    47 00000012 6E2E200A4E65772000 
    48 0000003F 0A00                   nlMessage db NEWLINE, NULL
    49 00000041 2000                   spaceMessage db " ",NULL
    50 00000043 496E76616C69642057-    outputMessage db "Invalid Word found: ",NULL
    51 00000043 6F726420666F756E64-
    52 00000043 3A2000             
    53 00000058 44454255473A2000       debugMsg db "DEBUG: ",NULL
    54 00000060 494E56414C49442000     invalidMsg db "INVALID ",NULL
    55 00000069 454E44494E472000       endMsg db "ENDING ",NULL
    56 00000071 50415448312000         path1Msg db "PATH1 ",NULL
    57 00000078 4245464F52455F434D-    beforeCmpMsg db "BEFORE_CMP ",NULL
    58 00000078 502000             
    59 00000084 5245545F5452554520-    retTrueMsg db "RET_TRUE ",NULL
    60 00000084 00                 
    61 0000008E 4E4F545F312000         not1Msg db "NOT_1 ",NULL
    62                                 BUFFSIZE equ 500000
    63 00000095 002F685900000000       maxLIMIT dq 1500000000
    64 0000009D 00D197A6FFFFFFFF       minLIMIT dq -1500000000
    65                                 
    66                                 [section .bss]
    67                                 
    68 00000000 <gap>                  BUFFER resb BUFFSIZE
    69                                 
    70                                 [section .text]
    71                                 
    72                                 [global checkParams]
    73                                 checkParams:
    74                                 
    75                                 
    76                                 
    77                                 
    78                                 
    79                                 
    80 00000000 55                      push rbp
    81 00000001 4889E5                  mov rbp, rsp
    82 00000004 53                      push rbx
    83 00000005 4154                    push r12
    84 00000007 4155                    push r13
    85 00000009 4156                    push r14
    86 0000000B 4157                    push r15
    87                                 
    88 0000000D 4989F0                  mov r8, rsi
    89 00000010 4989D1                  mov r9, rdx
    90 00000013 4989CA                  mov r10, rcx
    91                                 
    92                                 
    93 00000016 4883FF05                cmp rdi, 5
    94 0000001A 0F857D000000            jne fail
    95                                 
    96                                 
    97 00000020 498B5808                mov rbx, qword [r8 + 8]
    98                                 
    99                                 
   100                                 
   101 00000024 803B2D                  cmp byte [rbx], '-'
   102 00000027 7578                    jne fail
   103 00000029 807B0166                cmp byte [rbx + 1], 'f'
   104 0000002D 7572                    jne fail
   105 0000002F 807B0231                cmp byte [rbx + 2], '1'
   106 00000033 756C                    jne fail
   107 00000035 807B0300                cmp byte [rbx + 3], 0
   108 00000039 7566                    jne fail
   109                                 
   110 0000003B 498B5818                mov rbx, qword [r8 + 24]
   111 0000003F 803B2D                  cmp byte [rbx], '-'
   112 00000042 755D                    jne fail
   113 00000044 807B0166                cmp byte [rbx + 1], 'f'
   114 00000048 7557                    jne fail
   115 0000004A 807B0232                cmp byte [rbx + 2], '2'
   116 0000004E 7551                    jne fail
   117 00000050 807B0300                cmp byte [rbx + 3], 0
   118 00000054 754B                    jne fail
   119                                 
   120 00000056 498B7810                mov rdi, qword [r8 + 16]
   121 0000005A 48C7C641020000          mov rsi, O_CREAT | O_WRONLY | O_TRUNC
   122 00000061 48C7C2A4010000          mov rdx, 0644
   123 00000068 48C7C055000000          mov rax, SYS_creat
   124 0000006F 0F05                    syscall
   125 00000071 4883F800                cmp rax, 0
   126 00000075 7C2A                    jl fail
   127 00000077 498901                  mov qword [r9], rax
   128                                 
   129 0000007A 498B7820                mov rdi, qword [r8 + 32]
   130 0000007E 48C7C600000000          mov rsi, O_RDONLY
   131 00000085 4831D2                  xor rdx, rdx
   132 00000088 48C7C002000000          mov rax, SYS_open
   133 0000008F 0F05                    syscall
   134 00000091 4883F800                cmp rax, 0
   135 00000095 7C0A                    jl fail
   136 00000097 498902                  mov qword [r10], rax
   137                                 
   138 0000009A 48C7C001000000          mov rax, TRUE
   139 000000A1 EB05                    jmp doneWithCheckLoop
   140                                 
   141                                 fail:
   142 000000A3 48C7C000000000          mov rax, FALSE
   143                                 
   144                                 doneWithCheckLoop:
   145 000000AA 415F                    pop r15
   146 000000AC 415E                    pop r14
   147 000000AE 415D                    pop r13
   148 000000B0 415C                    pop r12
   149 000000B2 5B                      pop rbx
   150 000000B3 5D                      pop rbp
   151 000000B4 C3                     ret
   152                                 
   153                                 [global getWord]
   154                                 getWord:
   155 000000B5 55                      push rbp
   156 000000B6 4889E5                  mov rbp, rsp
   157 000000B9 53                      push rbx
   158 000000BA 4154                    push r12
   159 000000BC 4155                    push r13
   160 000000BE 4156                    push r14
   161 000000C0 4157                    push r15
   162                                 
   163 000000C2 4989FC                  mov r12, rdi
   164 000000C5 4989F5                  mov r13, rsi
   165 000000C8 4989D6                  mov r14, rdx
   166 000000CB 4989CF                  mov r15, rcx
   167 000000CE 48C7C300000000          mov rbx, 0
   168                                 
   169                                 skipSpacesLoop:
   170 000000D5 498B3F                  mov rdi, qword [r15]
   171 000000D8 48C7C6[00000000]        mov rsi, BUFFER
   172 000000DF 48C7C201000000          mov rdx, 1
   173 000000E6 48C7C000000000          mov rax, SYS_read
   174 000000ED 0F05                    syscall
   175 000000EF 4883F800                cmp rax, 0
   176 000000F3 0F8EC8000000            jle getEOF
   177                                 
   178 000000F9 8A0425[00000000]        mov al, byte [BUFFER]
   179 00000100 3C20                    cmp al, ' '
   180 00000102 74CF                    je skipSpacesLoop
   181 00000104 3C0A                    cmp al, 10
   182 00000106 74CB                    je skipSpacesLoop
   183 00000108 3C09                    cmp al, 9
   184 0000010A 74C7                    je skipSpacesLoop
   185                                 
   186 0000010C 4188041C                mov byte [r12 + rbx], al
   187 00000110 48FFC3                  inc rbx
   188 00000113 4C39F3                  cmp rbx, r14
   189 00000116 7D52                    jge wordTooLong
   190                                 
   191                                 readGetWordLoop:
   192 00000118 498B3F                  mov rdi, qword [r15]
   193 0000011B 48C7C6[00000000]        mov rsi, BUFFER
   194 00000122 48C7C201000000          mov rdx, 1
   195 00000129 48C7C000000000          mov rax, SYS_read
   196 00000130 0F05                    syscall
   197 00000132 4883F800                cmp rax, 0
   198 00000136 7E1F                    jle endwordofGetWordLoop
   199                                 
   200 00000138 8A0425[00000000]        mov al, byte [BUFFER]
   201 0000013F 3C20                    cmp al, ' '
   202 00000141 7414                    je endwordofGetWordLoop
   203 00000143 3C0A                    cmp al, 10
   204 00000145 7410                    je endwordofGetWordLoop
   205 00000147 3C09                    cmp al, 9
   206 00000149 740C                    je endwordofGetWordLoop
   207                                 
   208 0000014B 4C39F3                  cmp rbx, r14
   209 0000014E 7D1A                    jge wordTooLong
   210                                 
   211 00000150 4188041C                mov byte [r12 + rbx], al
   212 00000154 48FFC3                  inc rbx
   213 00000157 EBBD                    jmp readGetWordLoop
   214                                 
   215                                 endwordofGetWordLoop:
   216 00000159 41C6041C00              mov byte [r12 + rbx], 0
   217 0000015E 41C6450001              mov byte [r13], TRUE
   218 00000163 48C7C001000000          mov rax, TRUE
   219 0000016A EB60                    jmp done
   220                                 
   221                                 wordTooLong:
   222 0000016C 4188041C                mov byte [r12 + rbx], al
   223 00000170 48FFC3                  inc rbx
   224                                 
   225                                 wordTooLongLoop:
   226 00000173 498B3F                  mov rdi, qword [r15]
   227 00000176 48C7C6[00000000]        mov rsi, BUFFER
   228 0000017D 48C7C201000000          mov rdx, 1
   229 00000184 48C7C000000000          mov rax, SYS_read
   230 0000018B 0F05                    syscall
   231 0000018D 4883F800                cmp rax, 0
   232 00000191 7E1F                    jle endNotValid
   233                                 
   234 00000193 8A0425[00000000]        mov al, byte [BUFFER]
   235 0000019A 3C20                    cmp al, ' '
   236 0000019C 7414                    je endNotValid
   237 0000019E 3C0A                    cmp al, 10
   238 000001A0 7410                    je endNotValid
   239 000001A2 3C09                    cmp al, 9
   240 000001A4 740C                    je endNotValid
   241                                 
   242 000001A6 4C39F3                  cmp rbx, r14
   243 000001A9 7D05                    jge skipTilDone
   244 000001AB 4188041C                mov byte [r12 + rbx], al
   245 000001AF 48FFC3                  inc rbx
   246                                 
   247                                 skipTilDone:
   248 000001B2 EBBD                    jmp wordTooLongLoop
   249                                 
   250                                 endNotValid:
   251 000001B4 41C6041C00              mov byte [r12 + rbx], 0
   252 000001B9 41C6450000              mov byte [r13], FALSE
   253 000001BE 48C7C001000000          mov rax, TRUE
   254 000001C5 EB05                    jmp done
   255                                 
   256                                 getEOF:
   257 000001C7 48C7C000000000          mov rax, FALSE
   258                                 
   259                                 done:
   260 000001CE 415F                    pop r15
   261 000001D0 415E                    pop r14
   262 000001D2 415D                    pop r13
   263 000001D4 415C                    pop r12
   264 000001D6 5B                      pop rbx
   265 000001D7 5D                      pop rbp
   266 000001D8 C3                     ret
   267                                 
   268                                 [global printWord]
   269                                 printWord:
   270 000001D9 48C7C001000000          mov rax, SYS_write
   271 000001E0 0F05                    syscall
   272 000001E2 4080FE00                cmp sil, FALSE
   273 000001E6 7523                    jne printWord_done
   274                                 
   275 000001E8 57                      push rdi
   276 000001E9 56                      push rsi
   277 000001EA 52                      push rdx
   278 000001EB 50                      push rax
   279                                 
   280 000001EC 48C7C7[00000000]        mov rdi, outputMessage
   281 000001F3 E810010000              call printString
   282                                 
   283 000001F8 58                      pop rax
   284 000001F9 5A                      pop rdx
   285 000001FA 5E                      pop rsi
   286 000001FB 5F                      pop rdi
   287 000001FC E807010000              call printString
   288                                 
   289 00000201 48C7C7[00000000]        mov rdi, nlMessage
   290 00000208 E8FB000000              call printString
   291                                 
   292                                 printWord_done:
   293 0000020D C3                     ret
   294                                 
   295                                 [global getNumber]
   296                                 getNumber:
   297 0000020E 55                      push rbp
   298 0000020F 4889E5                  mov rbp, rsp
   299 00000212 53                      push rbx
   300 00000213 4154                    push r12
   301 00000215 4155                    push r13
   302 00000217 4156                    push r14
   303                                 
   304 00000219 4989FC                  mov r12, rdi
   305 0000021C 4989F5                  mov r13, rsi
   306 0000021F 4989D6                  mov r14, rdx
   307                                 
   308                                 
   309 00000222 41FF06                  inc dword [r14]
   310                                 
   311                                 
   312 00000225 48C7C7[00000000]        mov rdi, InputMessage
   313 0000022C E8D7000000              call printString
   314                                 
   315                                 
   316 00000231 418B1E                  mov ebx, dword [r14]
   317                                 
   318                                 
   319                                 
   320 00000234 83FB01                  cmp ebx, 1
   321 00000237 7431                    je invalidInput
   322 00000239 83FB02                  cmp ebx, 2
   323 0000023C 742C                    je invalidInput
   324 0000023E 83FB03                  cmp ebx, 3
   325 00000241 7427                    je invalidInput
   326 00000243 83FB07                  cmp ebx, 7
   327 00000246 7422                    je invalidInput
   328 00000248 83FB0E                  cmp ebx, 14
   329 0000024B 741D                    je invalidInput
   330                                 
   331                                 
   332 0000024D 83FB10                  cmp ebx, 16
   333 00000250 7D2D                    jge endInput
   334                                 
   335                                 
   336 00000252 41C6042431              mov byte [r12], '1'
   337 00000257 41C644240132            mov byte [r12 + 1], '2'
   338 0000025D 41C644240200            mov byte [r12 + 2], 0
   339 00000263 48C7C001000000          mov rax, TRUE
   340 0000026A EB1A                    jmp doneWithGetNumber
   341                                 
   342                                 invalidInput:
   343                                 
   344 0000026C 48C7C7[00000000]        mov rdi, badInput
   345 00000273 E890000000              call printString
   346                                 
   347 00000278 48C7C001000000          mov rax, TRUE
   348 0000027F EB05                    jmp doneWithGetNumber
   349                                 
   350                                 endInput:
   351 00000281 48C7C000000000          mov rax, FALSE
   352                                 
   353                                 doneWithGetNumber:
   354 00000288 415E                    pop r14
   355 0000028A 415D                    pop r13
   356 0000028C 415C                    pop r12
   357 0000028E 5B                      pop rbx
   358 0000028F 5D                      pop rbp
   359 00000290 C3                     ret
   360                                 
   361                                 [global saveNumbers]
   362                                 saveNumbers:
   363 00000291 55                      push rbp
   364 00000292 4889E5                  mov rbp, rsp
   365 00000295 53                      push rbx
   366 00000296 4154                    push r12
   367 00000298 4155                    push r13
   368                                 
   369 0000029A 4989FC                  mov r12, rdi
   370 0000029D 4989F5                  mov r13, rsi
   371 000002A0 4889D3                  mov rbx, rdx
   372                                 
   373 000002A3 488B3B                  mov rdi, qword [rbx]
   374 000002A6 4C89E6                  mov rsi, r12
   375 000002A9 4C89EA                  mov rdx, r13
   376 000002AC 48C7C001000000          mov rax, SYS_write
   377 000002B3 0F05                    syscall
   378                                 
   379 000002B5 488B3B                  mov rdi, qword [rbx]
   380 000002B8 48C7C6[00000000]        mov rsi, nlMessage
   381 000002BF 48C7C201000000          mov rdx, 1
   382 000002C6 48C7C001000000          mov rax, SYS_write
   383 000002CD 0F05                    syscall
   384                                 
   385 000002CF 415D                    pop r13
   386 000002D1 415C                    pop r12
   387 000002D3 5B                      pop rbx
   388 000002D4 5D                      pop rbp
   389 000002D5 48C7C001000000          mov rax, TRUE
   390 000002DC C3                     ret
   391                                 
   392                                 [global closeFile]
   393                                 closeFile:
   394 000002DD 55                      push rbp
   395 000002DE 4889E5                  mov rbp, rsp
   396 000002E1 53                      push rbx
   397                                 
   398 000002E2 4889FB                  mov rbx, rdi
   399 000002E5 488B3B                  mov rdi, qword [rbx]
   400 000002E8 48C7C003000000          mov rax, SYS_close
   401 000002EF 0F05                    syscall
   402 000002F1 5B                      pop rbx
   403 000002F2 5D                      pop rbp
   404 000002F3 C3                     ret
   405                                 
   406                                 [global getLength]
   407                                 getLength:
   408 000002F4 48C7C000000000          mov rax, 0
   409                                  strCountLoop2:
   410 000002FB 803C0700                cmp byte [rdi+rax], NULL
   411 000002FF 7403                    je strCountLoopDone2
   412 00000301 48FFC0                  inc rax
   413 00000304 EBF3                    jmp strCountLoop2
   414                                  strCountLoopDone2:
   415 00000306 4883F800                cmp rax, 0
   416 0000030A 74FE                    je printStringDone2
   417                                  printStringDone2:
   418 0000030C C3                     ret
   419                                 
   420                                 [global printString]
   421                                 printString:
   422                                 
   423                                 
   424                                 
   425 0000030D 48C7C200000000          mov rdx, 0
   426                                  strCountLoop:
   427 00000314 803C1700                cmp byte [rdi+rdx], NULL
   428 00000318 7403                    je strCountLoopDone
   429 0000031A 48FFC2                  inc rdx
   430 0000031D EBF3                    jmp strCountLoop
   431                                  strCountLoopDone:
   432 0000031F 4883FA00                cmp rdx, 0
   433 00000323 7411                    je printStringDone
   434                                 
   435                                 
   436                                 
   437                                 
   438 00000325 48C7C001000000          mov rax, SYS_write
   439 0000032C 4889FE                  mov rsi, rdi
   440 0000032F 48C7C701000000          mov rdi, STDOUT
   441                                 
   442 00000336 0F05                    syscall
   443                                 
   444                                 
   445                                 
   446                                 
   447                                  printStringDone:
   448 00000338 C3                     ret
