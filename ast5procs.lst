     1                                 %line 1+1 ast5procs.asm
     2                                 
     3                                 
     4                                 
     5                                 
     6                                 
     7                                 
     8                                 
     9                                 
    10                                 
    11                                 
    12                                 
    13                                 [section .data]
    14                                 TRUE equ 1
    15                                 FALSE equ 0
    16                                 NULL equ 0
    17                                 LF equ 10
    18                                 NEWLINE equ 10
    19                                 
    20                                 SYS_read equ 0
    21                                 SYS_write equ 1
    22                                 SYS_open equ 2
    23                                 SYS_close equ 3
    24                                 SYS_fork equ 57
    25                                 SYS_exit equ 60
    26                                 SYS_creat equ 85
    27                                 SYS_time equ 201
    28                                 
    29                                 O_RDONLY equ 000000
    30                                 O_WRONLY equ 000001
    31                                 O_RDWR equ 000002
    32                                 
    33                                 STDIN equ 0
    34                                 STDOUT equ 1
    35                                 STDERR equ 2
    36                                 
    37                                 O_CREAT equ 0x40
    38                                 O_TRUNC equ 0x200
    39                                 O_APPEND equ 0x400
    40                                 
    41 00000000 496E7075742061206E-    InputMessage db "Input a number: ",NEWLINE, NULL
    42 00000000 756D6265723A200A00 
    43 00000012 596F75206761766520-    badInput db "You gave an invalid number, try again. ",NEWLINE, "New ", NULL
    44 00000012 616E20696E76616C69-
    45 00000012 64206E756D6265722C-
    46 00000012 207472792061676169-
    47 00000012 6E2E200A4E65772000 
    48 0000003F 0A00                   nlMessage db NEWLINE, NULL
    49 00000041 2000                   spaceMessage db " ",NULL
    50 00000043 496E76616C69642057-    outputMessage db "Invalid Word found: ",NULL
    51 00000043 6F726420666F756E64-
    52 00000043 3A2000             
    53                                 BUFFSIZE equ 500000
    54 00000058 002F685900000000       maxLIMIT dq 1500000000
    55 00000060 00D197A6FFFFFFFF       minLIMIT dq -1500000000
    56                                 
    57                                 [section .bss]
    58                                 
    59 00000000 <gap>                  BUFFER resb BUFFSIZE
    60                                 
    61                                 [section .text]
    62                                 
    63                                 [global checkParams]
    64                                 checkParams:
    65                                 
    66                                 
    67                                 
    68                                 
    69                                 
    70                                 
    71 00000000 55                      push rbp
    72 00000001 4889E5                  mov rbp, rsp
    73 00000004 53                      push rbx
    74 00000005 4154                    push r12
    75 00000007 4155                    push r13
    76 00000009 4156                    push r14
    77 0000000B 4157                    push r15
    78                                 
    79 0000000D 4989F0                  mov r8, rsi
    80 00000010 4989D1                  mov r9, rdx
    81 00000013 4989CA                  mov r10, rcx
    82                                 
    83                                 
    84 00000016 4883FF05                cmp rdi, 5
    85 0000001A 0F857D000000            jne fail
    86                                 
    87                                 
    88 00000020 498B5808                mov rbx, qword [r8 + 8]
    89                                 
    90                                 
    91                                 
    92 00000024 803B2D                  cmp byte [rbx], '-'
    93 00000027 7578                    jne fail
    94 00000029 807B0166                cmp byte [rbx + 1], 'f'
    95 0000002D 7572                    jne fail
    96 0000002F 807B0231                cmp byte [rbx + 2], '1'
    97 00000033 756C                    jne fail
    98 00000035 807B0300                cmp byte [rbx + 3], 0
    99 00000039 7566                    jne fail
   100                                 
   101 0000003B 498B5818                mov rbx, qword [r8 + 24]
   102 0000003F 803B2D                  cmp byte [rbx], '-'
   103 00000042 755D                    jne fail
   104 00000044 807B0166                cmp byte [rbx + 1], 'f'
   105 00000048 7557                    jne fail
   106 0000004A 807B0232                cmp byte [rbx + 2], '2'
   107 0000004E 7551                    jne fail
   108 00000050 807B0300                cmp byte [rbx + 3], 0
   109 00000054 754B                    jne fail
   110                                 
   111 00000056 498B7810                mov rdi, qword [r8 + 16]
   112 0000005A 48C7C641020000          mov rsi, O_CREAT | O_WRONLY | O_TRUNC
   113 00000061 48C7C2A4010000          mov rdx, 0644
   114 00000068 48C7C055000000          mov rax, SYS_creat
   115 0000006F 0F05                    syscall
   116 00000071 4883F800                cmp rax, 0
   117 00000075 7C2A                    jl fail
   118 00000077 498901                  mov qword [r9], rax
   119                                 
   120 0000007A 498B7820                mov rdi, qword [r8 + 32]
   121 0000007E 48C7C600000000          mov rsi, O_RDONLY
   122 00000085 4831D2                  xor rdx, rdx
   123 00000088 48C7C002000000          mov rax, SYS_open
   124 0000008F 0F05                    syscall
   125 00000091 4883F800                cmp rax, 0
   126 00000095 7C0A                    jl fail
   127 00000097 498902                  mov qword [r10], rax
   128                                 
   129 0000009A 48C7C001000000          mov rax, TRUE
   130 000000A1 EB05                    jmp doneWithCheckLoop
   131                                 
   132                                 fail:
   133 000000A3 48C7C000000000          mov rax, FALSE
   134                                 
   135                                 doneWithCheckLoop:
   136 000000AA 415F                    pop r15
   137 000000AC 415E                    pop r14
   138 000000AE 415D                    pop r13
   139 000000B0 415C                    pop r12
   140 000000B2 5B                      pop rbx
   141 000000B3 5D                      pop rbp
   142 000000B4 C3                     ret
   143                                 
   144                                 [global getWord]
   145                                 getWord:
   146 000000B5 55                      push rbp
   147 000000B6 4889E5                  mov rbp, rsp
   148 000000B9 53                      push rbx
   149 000000BA 4154                    push r12
   150 000000BC 4155                    push r13
   151 000000BE 4156                    push r14
   152 000000C0 4157                    push r15
   153                                 
   154 000000C2 4989FC                  mov r12, rdi
   155 000000C5 4989F5                  mov r13, rsi
   156 000000C8 4989D6                  mov r14, rdx
   157 000000CB 4989CF                  mov r15, rcx
   158 000000CE 48C7C300000000          mov rbx, 0
   159                                 
   160                                 skipSpacesLoop:
   161 000000D5 498B3F                  mov rdi, qword [r15]
   162 000000D8 48C7C6[00000000]        mov rsi, BUFFER
   163 000000DF 48C7C201000000          mov rdx, 1
   164 000000E6 48C7C000000000          mov rax, SYS_read
   165 000000ED 0F05                    syscall
   166 000000EF 4883F800                cmp rax, 0
   167 000000F3 0F8EC8000000            jle getEOF
   168                                 
   169 000000F9 8A0425[00000000]        mov al, byte [BUFFER]
   170 00000100 3C20                    cmp al, ' '
   171 00000102 74CF                    je skipSpacesLoop
   172 00000104 3C0A                    cmp al, 10
   173 00000106 74CB                    je skipSpacesLoop
   174 00000108 3C09                    cmp al, 9
   175 0000010A 74C7                    je skipSpacesLoop
   176                                 
   177 0000010C 4188041C                mov byte [r12 + rbx], al
   178 00000110 48FFC3                  inc rbx
   179 00000113 4C39F3                  cmp rbx, r14
   180 00000116 7D52                    jge wordTooLong
   181                                 
   182                                 readGetWordLoop:
   183 00000118 498B3F                  mov rdi, qword [r15]
   184 0000011B 48C7C6[00000000]        mov rsi, BUFFER
   185 00000122 48C7C201000000          mov rdx, 1
   186 00000129 48C7C000000000          mov rax, SYS_read
   187 00000130 0F05                    syscall
   188 00000132 4883F800                cmp rax, 0
   189 00000136 7E1F                    jle endwordofGetWordLoop
   190                                 
   191 00000138 8A0425[00000000]        mov al, byte [BUFFER]
   192 0000013F 3C20                    cmp al, ' '
   193 00000141 7414                    je endwordofGetWordLoop
   194 00000143 3C0A                    cmp al, 10
   195 00000145 7410                    je endwordofGetWordLoop
   196 00000147 3C09                    cmp al, 9
   197 00000149 740C                    je endwordofGetWordLoop
   198                                 
   199 0000014B 4C39F3                  cmp rbx, r14
   200 0000014E 7D1A                    jge wordTooLong
   201                                 
   202 00000150 4188041C                mov byte [r12 + rbx], al
   203 00000154 48FFC3                  inc rbx
   204 00000157 EBBD                    jmp readGetWordLoop
   205                                 
   206                                 endwordofGetWordLoop:
   207 00000159 41C6041C00              mov byte [r12 + rbx], 0
   208 0000015E 41C6450001              mov byte [r13], TRUE
   209 00000163 48C7C001000000          mov rax, TRUE
   210 0000016A EB60                    jmp done
   211                                 
   212                                 wordTooLong:
   213 0000016C 4188041C                mov byte [r12 + rbx], al
   214 00000170 48FFC3                  inc rbx
   215                                 
   216                                 wordTooLongLoop:
   217 00000173 498B3F                  mov rdi, qword [r15]
   218 00000176 48C7C6[00000000]        mov rsi, BUFFER
   219 0000017D 48C7C201000000          mov rdx, 1
   220 00000184 48C7C000000000          mov rax, SYS_read
   221 0000018B 0F05                    syscall
   222 0000018D 4883F800                cmp rax, 0
   223 00000191 7E1F                    jle endNotValid
   224                                 
   225 00000193 8A0425[00000000]        mov al, byte [BUFFER]
   226 0000019A 3C20                    cmp al, ' '
   227 0000019C 7414                    je endNotValid
   228 0000019E 3C0A                    cmp al, 10
   229 000001A0 7410                    je endNotValid
   230 000001A2 3C09                    cmp al, 9
   231 000001A4 740C                    je endNotValid
   232                                 
   233 000001A6 4C39F3                  cmp rbx, r14
   234 000001A9 7D05                    jge skipTilDone
   235 000001AB 4188041C                mov byte [r12 + rbx], al
   236 000001AF 48FFC3                  inc rbx
   237                                 
   238                                 skipTilDone:
   239 000001B2 EBBD                    jmp wordTooLongLoop
   240                                 
   241                                 endNotValid:
   242 000001B4 41C6041C00              mov byte [r12 + rbx], 0
   243 000001B9 41C6450000              mov byte [r13], FALSE
   244 000001BE 48C7C001000000          mov rax, TRUE
   245 000001C5 EB05                    jmp done
   246                                 
   247                                 getEOF:
   248 000001C7 48C7C000000000          mov rax, FALSE
   249                                 
   250                                 done:
   251 000001CE 415F                    pop r15
   252 000001D0 415E                    pop r14
   253 000001D2 415D                    pop r13
   254 000001D4 415C                    pop r12
   255 000001D6 5B                      pop rbx
   256 000001D7 5D                      pop rbp
   257 000001D8 C3                     ret
   258                                 
   259                                 [global printWord]
   260                                 printWord:
   261 000001D9 48C7C001000000          mov rax, SYS_write
   262 000001E0 0F05                    syscall
   263 000001E2 4080FE00                cmp sil, FALSE
   264 000001E6 7523                    jne printWord_done
   265                                 
   266 000001E8 57                      push rdi
   267 000001E9 56                      push rsi
   268 000001EA 52                      push rdx
   269 000001EB 50                      push rax
   270                                 
   271 000001EC 48C7C7[00000000]        mov rdi, outputMessage
   272 000001F3 E863010000              call printString
   273                                 
   274 000001F8 58                      pop rax
   275 000001F9 5A                      pop rdx
   276 000001FA 5E                      pop rsi
   277 000001FB 5F                      pop rdi
   278 000001FC E85A010000              call printString
   279                                 
   280 00000201 48C7C7[00000000]        mov rdi, nlMessage
   281 00000208 E84E010000              call printString
   282                                 
   283                                 printWord_done:
   284 0000020D C3                     ret
   285                                 
   286                                 [global getNumber]
   287                                 getNumber:
   288 0000020E 55                      push rbp
   289 0000020F 4889E5                  mov rbp, rsp
   290 00000212 53                      push rbx
   291 00000213 4154                    push r12
   292 00000215 4155                    push r13
   293 00000217 4156                    push r14
   294                                 
   295 00000219 4989FC                  mov r12, rdi
   296 0000021C 4989F5                  mov r13, rsi
   297 0000021F 4989D6                  mov r14, rdx
   298                                 
   299 00000222 49833E00                cmp qword [r14], 0
   300 00000226 7507                    jne increment
   301 00000228 49C70601000000          mov qword [r14], 1
   302 0000022F EB01                    jmp afterIncrement
   303                                 
   304                                 increment:
   305 00000231 49FF06                  inc qword [r14]
   306                                 
   307                                 afterIncrement:
   308 00000234 48C7C7[00000000]        mov rdi, InputMessage
   309 0000023B E81B010000              call printString
   310 00000240 498B06                  mov rax, qword [r14]
   311 00000243 4883F801                cmp rax, 1
   312 00000247 7474                    je invalidOption
   313 00000249 4883F802                cmp rax, 2
   314 0000024D 746E                    je invalidOption
   315 0000024F 4883F803                cmp rax, 3
   316 00000253 7468                    je invalidOption
   317 00000255 4883F804                cmp rax, 4
   318 00000259 7448                    je validOption
   319 0000025B 4883F805                cmp rax, 5
   320 0000025F 7442                    je validOption
   321 00000261 4883F806                cmp rax, 6
   322 00000265 743C                    je validOption
   323 00000267 4883F807                cmp rax, 7
   324 0000026B 7450                    je invalidOption
   325 0000026D 4883F808                cmp rax, 8
   326 00000271 7430                    je validOption
   327 00000273 4883F809                cmp rax, 9
   328 00000277 742A                    je validOption
   329 00000279 4883F80A                cmp rax, 10
   330 0000027D 7424                    je validOption
   331 0000027F 4883F80B                cmp rax, 11
   332 00000283 741E                    je validOption
   333 00000285 4883F80C                cmp rax, 12
   334 00000289 7418                    je validOption
   335 0000028B 4883F80D                cmp rax, 13
   336 0000028F 7412                    je validOption
   337 00000291 4883F80E                cmp rax, 14
   338 00000295 7426                    je invalidOption
   339 00000297 4883F80F                cmp rax, 15
   340 0000029B 7406                    je validOption
   341 0000029D 4883F814                cmp rax, 20
   342 000002A1 7D2F                    jge endInput
   343 000002A3 EBFE                    jmp validOption
   344                                 
   345                                 validOption:
   346 000002A5 41C6042431              mov byte [r12], '1'
   347 000002AA 41C644240132            mov byte [r12 + 1], '2'
   348 000002B0 41C644240200            mov byte [r12 + 2], 0
   349 000002B6 48C7C001000000          mov rax, TRUE
   350 000002BD EB1A                    jmp doneWithGetNumber
   351                                 
   352                                 invalidOption:
   353 000002BF 48C7C7[00000000]        mov rdi, badInput
   354 000002C6 E890000000              call printString
   355 000002CB 48C7C001000000          mov rax, TRUE
   356 000002D2 EB05                    jmp doneWithGetNumber
   357                                 
   358                                 endInput:
   359 000002D4 48C7C000000000          mov rax, FALSE
   360                                 
   361                                 doneWithGetNumber:
   362 000002DB 415E                    pop r14
   363 000002DD 415D                    pop r13
   364 000002DF 415C                    pop r12
   365 000002E1 5B                      pop rbx
   366 000002E2 5D                      pop rbp
   367 000002E3 C3                     ret
   368                                 
   369                                 [global saveNumbers]
   370                                 saveNumbers:
   371 000002E4 55                      push rbp
   372 000002E5 4889E5                  mov rbp, rsp
   373 000002E8 53                      push rbx
   374 000002E9 4154                    push r12
   375 000002EB 4155                    push r13
   376                                 
   377 000002ED 4989FC                  mov r12, rdi
   378 000002F0 4989F5                  mov r13, rsi
   379 000002F3 4889D3                  mov rbx, rdx
   380                                 
   381 000002F6 488B3B                  mov rdi, qword [rbx]
   382 000002F9 4C89E6                  mov rsi, r12
   383 000002FC 4C89EA                  mov rdx, r13
   384 000002FF 48C7C001000000          mov rax, SYS_write
   385 00000306 0F05                    syscall
   386                                 
   387 00000308 488B3B                  mov rdi, qword [rbx]
   388 0000030B 48C7C6[00000000]        mov rsi, nlMessage
   389 00000312 48C7C201000000          mov rdx, 1
   390 00000319 48C7C001000000          mov rax, SYS_write
   391 00000320 0F05                    syscall
   392                                 
   393 00000322 415D                    pop r13
   394 00000324 415C                    pop r12
   395 00000326 5B                      pop rbx
   396 00000327 5D                      pop rbp
   397 00000328 48C7C001000000          mov rax, TRUE
   398 0000032F C3                     ret
   399                                 
   400                                 [global closeFile]
   401                                 closeFile:
   402 00000330 55                      push rbp
   403 00000331 4889E5                  mov rbp, rsp
   404 00000334 53                      push rbx
   405                                 
   406 00000335 4889FB                  mov rbx, rdi
   407 00000338 488B3B                  mov rdi, qword [rbx]
   408 0000033B 48C7C003000000          mov rax, SYS_close
   409 00000342 0F05                    syscall
   410 00000344 5B                      pop rbx
   411 00000345 5D                      pop rbp
   412 00000346 C3                     ret
   413                                 
   414                                 [global getLength]
   415                                 getLength:
   416 00000347 48C7C000000000          mov rax, 0
   417                                  strCountLoop2:
   418 0000034E 803C0700                cmp byte [rdi+rax], NULL
   419 00000352 7403                    je strCountLoopDone2
   420 00000354 48FFC0                  inc rax
   421 00000357 EBF3                    jmp strCountLoop2
   422                                  strCountLoopDone2:
   423 00000359 4883F800                cmp rax, 0
   424 0000035D 74FE                    je printStringDone2
   425                                  printStringDone2:
   426 0000035F C3                     ret
   427                                 
   428                                 [global printString]
   429                                 printString:
   430                                 
   431                                 
   432                                 
   433 00000360 48C7C200000000          mov rdx, 0
   434                                  strCountLoop:
   435 00000367 803C1700                cmp byte [rdi+rdx], NULL
   436 0000036B 7403                    je strCountLoopDone
   437 0000036D 48FFC2                  inc rdx
   438 00000370 EBF3                    jmp strCountLoop
   439                                  strCountLoopDone:
   440 00000372 4883FA00                cmp rdx, 0
   441 00000376 7411                    je printStringDone
   442                                 
   443                                 
   444                                 
   445                                 
   446 00000378 48C7C001000000          mov rax, SYS_write
   447 0000037F 4889FE                  mov rsi, rdi
   448 00000382 48C7C701000000          mov rdi, STDOUT
   449                                 
   450 00000389 0F05                    syscall
   451                                 
   452                                 
   453                                 
   454                                 
   455                                  printStringDone:
   456 0000038B C3                     ret
