section .data
TRUE equ 1
FALSE equ 0
NULL equ 0
LF equ 10
NEWLINE equ 10

SYS_read	equ	0			; system call code for read
SYS_write	equ	1			; system call code for write
SYS_open	equ	2			; system call code for file open
SYS_close	equ	3			; system call code for file close
SYS_fork	equ	57			; system call code for fork
SYS_exit	equ	60			; system call code for terminate
SYS_creat	equ	85			; system call code for file open/create
SYS_time	equ	201			; system call code for get time

O_RDONLY	equ	000000q			; file permission - read only
O_WRONLY	equ	000001q			; file permission - write only
O_RDWR		equ	000002q			; file permission - read and write

STDIN		equ	0			; standard input
STDOUT		equ	1			; standard output
STDERR		equ	2			; standard error

O_CREAT		equ	0x40
O_TRUNC		equ	0x200
O_APPEND	equ	0x400

InputMessage db "Input a number: ",NEWLINE, NULL
badInput db "You gave an invalid number, try again. ",NEWLINE, "New ", NULL
nlMessage db NEWLINE, NULL 
spaceMessage db " ",NULL
hereMSG db "HERE - 1", NEWLINE,NULL

BUFFSIZE equ 500000
maxLIMIT dq 1500000000
minLIMIT dq -1500000000
buffIndex dq BUFFSIZE
buffCurr dq BUFFSIZE
wasEOF db 0


section .bss

buffer resb BUFFSIZE

section .text

global checkParams
checkParams:

ret

global getWord
getWord:

ret

global printWord
printWord:

ret

global getNumber
getNumber:

ret

global saveNumbers
saveNumbers:

ret

global closeFile
closeFile:

ret

; rdi - file name, null terminated
; rsi - persmissions for file
global openAndCreateFiles
openAndCreateFiles:
    mov rax, SYS_open
    syscall
    cmp rax, 0
    jge allGoodFiles

    mov rax, SYS_creat
    ; rdi and rsi are set up
    syscall


    allGoodFiles:
ret

global getLength
getLength:
        mov	rax, 0
    strCountLoop2:
        cmp	byte [rdi+rax], NULL
        je	strCountLoopDone2
        inc	rax
        jmp	strCountLoop2
    strCountLoopDone2:
        cmp	rax, 0
        je	printStringDone2
    printStringDone2:
ret

global	printString
printString:
    ; -----
    ;  Count characters to write.

        mov	rdx, 0
    strCountLoop:
        cmp	byte [rdi+rdx], NULL
        je	strCountLoopDone
        inc	rdx
        jmp	strCountLoop
    strCountLoopDone:
        cmp	rdx, 0
        je	printStringDone

    ; -----
    ;  Call OS to output string.

        mov	rax, SYS_write			; system code for write()
        mov	rsi, rdi			; address of characters to write
        mov	rdi, STDOUT			; file descriptor for standard in
                            ; rdx=count to write, set above
        syscall					; system call

    ; -----
    ;  String printed, return to calling routine.

    printStringDone:
ret

global convertDuoDecimal
convertDuoDecimal:
    push r12
    push r13
    push r14
    push r15

	mov r13, 0 ; index
	mov r12, 12 ; power
	mov r14, 0
	mov r10, 0

    skipSpaces:
        cmp byte[rdi+r13], NULL
        je invalidChar

        cmp byte[rdi+r13], ' '
		jne checkSign

        inc r13
        jmp skipSpaces

    checkSign:
        cmp byte[rdi+r13], '-'
        jne checkPosSign
        mov r15, -1
        inc r13

    checkPosSign:
        cmp byte[rdi+r13], '+'
        jne startLoop
        mov r15, 1
        inc r13

	startLoop:
		cmp byte[rdi+r13], NULL
		je endLoop

		cmp byte[rdi+r13], '0'
		jl invalidChar

		cmp byte[rdi+r13], '9'
		jg upperCheck

		mov r14b, byte[rdi+r13]
		sub r14b, '0'

		jmp convert

		upperCheck:

			cmp byte[rdi+r13], 'A'
			jl invalidChar

			cmp byte[rdi+r13], 'B'
			jg checkLowerCase

			mov r14b, byte[rdi+r13]
			sub r14b, 55
            jmp convert

        checkLowerCase:
			cmp byte[rdi+r13], 'a'
			jl invalidChar

			cmp byte[rdi+r13], 'b'
			jg invalidChar

			mov r14b, byte[rdi+r13]
			sub r14b, 87

		convert:
			push rax
            push rdx
            mov rdx, 0
			mov rax, r10
			mul r12
			
			movzx r11, r14b
			add rax, r11

			mov r10, rax
			mov rdx, 0
            pop rdx
			pop rax

		nextIndex:
			inc r13
			jmp startLoop
	endLoop:

    push rax
    push rdx
    mov rax, r10
    imul r15
    pop rdx
    mov r10, rax
    pop rax

    cmp r10, rsi
    jg overLimit

    cmp r10, rdx
    jl overLimit

    jmp noIssues
    
    invalidChar:
        mov rax, -1
        jmp end

    underLimit:
        mov rax, -1
        jmp end

    overLimit:
        mov rax, -1
        jmp end

    noIssues:
    mov rax, TRUE

    end:
        pop r15
        pop r14
        pop r13
        pop r12
ret